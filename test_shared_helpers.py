"""Shared test helpers for MNIST model tests (MLP and CNN)."""
import torch

def assert_training_step_decreases_loss(model, loader, optimizer_cls):
    """Assert that a single training step decreases (or does not explode) the loss."""
    optimizer = optimizer_cls(model.parameters(), lr=0.01)
    criterion = torch.nn.CrossEntropyLoss()
    model.train()
    images, labels = next(iter(loader))
    outputs = model(images)
    loss1 = criterion(outputs, labels)
    optimizer.zero_grad()
    loss1.backward()
    optimizer.step()
    outputs = model(images)
    loss2 = criterion(outputs, labels)
    assert (
        loss2.item() <= loss1.item() + 1e-3
    ), f"Loss did not decrease or exploded: {loss1.item()} -> {loss2.item()}"

def assert_compile_output_shape(model, backend, batch_size=8):
    """Assert that torch.compile model produces correct output shape."""
    compiled_model = torch.compile(model, backend=backend)
    x = torch.randn(batch_size, 1, 28, 28)
    output = compiled_model(x)
    assert output.shape == (batch_size, 10)

def assert_compile_training_step(model, loader, optimizer_cls, backend):
    """Assert that torch.compile model can perform a training step."""
    compiled_model = torch.compile(model, backend=backend)
    optimizer = optimizer_cls(compiled_model.parameters(), lr=0.01)
    criterion = torch.nn.CrossEntropyLoss()
    compiled_model.train()
    images, labels = next(iter(loader))
    outputs = compiled_model(images)
    loss1 = criterion(outputs, labels)
    optimizer.zero_grad()
    loss1.backward()
    optimizer.step()
    outputs = compiled_model(images)
    loss2 = criterion(outputs, labels)
    assert (
        loss2.item() <= loss1.item() + 1e-3
    ), f"Loss did not decrease or exploded: {loss1.item()} -> {loss2.item()}"
