"""Inference script for MNIST models (MLP and CNN) with checkpointing and CLI support."""
import logging
import os

import torch
from torch.utils.data import DataLoader, Subset
from torchvision import transforms

from mnist_cli_helpers import parse_and_load_model_with_checkpoint, load_and_prepare_model
from mnist_data_helpers import PatchedMNIST  # Use shared PatchedMNIST

# Configure logging
LOG_LEVEL = os.environ.get("MNIST_LOG_LEVEL", "INFO").upper()
logging.basicConfig(
    format="%(asctime)s [%(levelname)s] %(message)s",
    level=getattr(logging, LOG_LEVEL, logging.INFO),
)
logger = logging.getLogger("mnist_infer")


def get_test_samples(
    num_samples: int = 5, data_dir: str = "./mnist_data"
) -> DataLoader:
    """
    Loads a subset of MNIST test samples for inference.
    Args:
        num_samples (int): The number of samples to load.
        data_dir (str): The directory containing the MNIST data.
    Returns:
        DataLoader: A DataLoader object for the subset of test samples.
    """
    transform = transforms.Compose(
        [
            transforms.ToTensor(),
        ]
    )
    test_dataset = PatchedMNIST(
        root=data_dir, train=False, download=True, transform=transform
    )
    indices = list(range(num_samples))
    subset = Subset(test_dataset, indices)
    loader = DataLoader(subset, batch_size=1, shuffle=False)
    return loader


def infer_main(args=None) -> None:
    """
    Main inference function. Handles command line arguments, model loading, and inference loop.
    Args:
        args (argparse.Namespace): Command line arguments. If None, parses arguments.
    """
    if args is None:
        args, _, _, _, _ = parse_and_load_model_with_checkpoint(
            description="MNIST inference demo.", default_batch_size=1, default_test_mode=False
        )
    model, _, _, start_epoch = load_and_prepare_model(args, logger)
    if start_epoch is None:
        return
    num_samples = 8 if getattr(args, 'test_mode', False) else 5
    test_loader = get_test_samples(num_samples=num_samples, data_dir=args.data_dir)
    logger.info("Running inference on %d MNIST test samples:", num_samples)
    with torch.no_grad():
        for idx, (img, label) in enumerate(test_loader):
            output = model(img)
            pred = output.argmax(dim=1).item()
            logger.info(
                "Sample %d: True label = %d, Predicted = %d",
                idx + 1,
                label.item(),
                pred,
            )


if __name__ == "__main__":
    try:
        infer_main()
    except KeyboardInterrupt:
        logger.warning("Inference interrupted by user.")
    except Exception as e:  # pylint: disable=broad-exception-caught
        # Catching all exceptions to ensure uncaught errors are logged in CLI usage.
        logger.error("Uncaught exception: %s", e, exc_info=True)
        print(f"[ERROR] {e}")
