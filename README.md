# MNIST PyTorch Training Suite

A professional, test-driven, and robust MNIST training, validation, and inference suite supporting both MLP and minimal CNN architectures, with checkpointing, CLI, and optional PyTorch 2.x compilation.

---

## Features
- **Modular scripts** for training (`mnist_train.py`), validation (`mnist_validate.py`), and inference (`mnist_infer.py`)
- **Model selection**: MLP or minimal CNN (parameterized)
- **Robust checkpointing** with architecture consistency checks
- **Test-driven development**: Extensive tests for all major components
- **Optional model compilation**: Use `torch.compile` for speed (PyTorch 2.x+)
- **Fully parameterized**: Batch size, learning rate, data/checkpoint paths, and model hyperparameters
- **Logging**: Informative logging throughout

---

## Installation

1. **Clone the repository** and `cd` into the project directory.
2. **Install Python 3.8+** (recommended: Python 3.10+).
3. **Install dependencies:**
   ```sh
   pip install -r requirements.txt
   ```
4. **(Optional, for torch.compile on Windows)**: Install [Visual Studio Build Tools](https://visualstudio.microsoft.com/downloads/) with "Desktop development with C++". Ensure `cl.exe` is in your PATH.

---

## Usage

### **Training**
```sh
python mnist_train.py --model cnn --batch-size 64 --lr 0.01 --minutes 5 --compile
```
- Use `--model mlp` for the MLP.
- Use `--compile` to enable `torch.compile` (PyTorch 2.x+).
- All hyperparameters and paths are configurable via CLI.

### **Validation**
```sh
python mnist_validate.py --model cnn --batch-size 64 --compile
```

### **Inference**
```sh
python mnist_infer.py --model cnn --batch-size 1 --compile
```

---

## Checkpointing
- Checkpoints are saved in the `checkpoints/` directory by default.
- Model architecture is stored in the checkpoint for consistency.
- Use the `upgrade_checkpoints.py` utility to add missing metadata to old checkpoints if needed.

---

## Testing

Run all tests:
```sh
pytest -v
```
- Some tests are marked `@pytest.mark.slow` and may take longer.
- Tests requiring `torch.compile` will be skipped if not available or if a C++ compiler is missing.

---

## Reproducibility
- All dependencies are pinned in `requirements.txt`.
- Tests use fixed random seeds for reproducibility.

---

## Troubleshooting
- **torch.compile errors on Windows**: Ensure you have the MSVC C++ compiler (`cl.exe`) installed and available in your terminal.
- **Checkpoint architecture mismatch**: Use the same model parameters as when the checkpoint was created, or upgrade old checkpoints.

---

## Contributing
- Please lint and test your code before submitting a PR.
- (Optional) Set up pre-commit hooks and CI for automated checks.

---

