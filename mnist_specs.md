# MNIST Training Script - Specifications

## Overview
- The script will download the MNIST dataset and train a simple neural network on it.
- The script must run on a CPU-only machine (no GPU dependencies).
- The code will be fully annotated and written in a test-driven development (TDD) style.

## Requirements
1. **Dataset Handling**
    - Download the MNIST dataset if not already present.
    - Load the dataset into train and test splits.

2. **Model**
    - Define a simple feedforward neural network suitable for MNIST digit classification.
    - The model should be small enough to train quickly on CPU.

3. **Training**
    - Implement a training loop with configurable epochs and batch size.
    - Print training and validation accuracy after each epoch.

4. **Testing**
    - Evaluate the model on the test set after training.
    - Report final test accuracy.

5. **TDD Approach**
    - Write unit tests for each major component: data loading, model definition, training loop, and evaluation.
    - Use a Python testing framework (e.g., pytest or unittest).

6. **Documentation**
    - All code must be fully annotated with comments explaining each step.
    - Provide a README section in the script or as a separate file with instructions to run the code and tests.

## Constraints
- Must use only CPU-compatible libraries (e.g., PyTorch or TensorFlow with CPU backend, or Keras with TensorFlow CPU).
- No GPU-specific code or dependencies.
- Keep dependencies minimal and standard.

## Deliverables
- `mnist_train.py`: The main script for training and evaluating the model.
- `test_mnist.py`: The test suite for TDD.
- `README.md`: Instructions for running the script and tests. 