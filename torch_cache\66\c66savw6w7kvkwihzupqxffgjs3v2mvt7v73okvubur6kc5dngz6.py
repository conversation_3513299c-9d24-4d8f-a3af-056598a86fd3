# AOT ID: ['0_forward']
from ctypes import c_void_p, c_long, c_int
import torch
import math
import random
import os
import tempfile
from math import inf, nan
from cmath import nanj
from torch._inductor.hooks import run_intermediate_hooks
from torch._inductor.utils import maybe_profile
from torch._inductor.codegen.memory_planning import _align as align
from torch import device, empty_strided
from torch._inductor.async_compile import AsyncCompile
from torch._inductor.select_algorithm import extern_kernels
from torch._inductor.codegen.multi_kernel import MultiKernelCall

aten = torch.ops.aten
inductor_ops = torch.ops.inductor
_quantized = torch.ops._quantized
assert_size_stride = torch._C._dynamo.guards.assert_size_stride
empty_strided_cpu = torch._C._dynamo.guards._empty_strided_cpu
empty_strided_cuda = torch._C._dynamo.guards._empty_strided_cuda
empty_strided_xpu = torch._C._dynamo.guards._empty_strided_xpu
reinterpret_tensor = torch._C._dynamo.guards._reinterpret_tensor
alloc_from_pool = torch.ops.inductor._alloc_from_pool
async_compile = AsyncCompile()
empty_strided_p2p = torch._C._distributed_c10d._SymmetricMemory.empty_strided_p2p


cpp_fused_add_0 = async_compile.cpp_pybinding(['const int64_t*', 'const int64_t*', 'const int64_t*', 'const float*', 'const float*', 'int64_t*', 'int64_t*', 'int64_t*', 'float*', 'float*'], '''
#include "./torch_cache/pi/cpicxudqmdsjh5cm4klbtbrvy2cxwr7whxl3md2zzdjdf3orvfdf.h"
extern "C" __declspec(dllexport) void kernel(const int64_t* in_ptr0,
                       const int64_t* in_ptr1,
                       const int64_t* in_ptr2,
                       const float* in_ptr3,
                       const float* in_ptr4,
                       int64_t* out_ptr1,
                       int64_t* out_ptr3,
                       int64_t* out_ptr5,
                       float* out_ptr6,
                       float* out_ptr7)
{
    {
        {
            {
                auto tmp0 = in_ptr0[static_cast<int64_t>(0LL)];
                auto tmp1 = static_cast<int64_t>(1);
                auto tmp2 = decltype(tmp0)(tmp0 + tmp1);
                out_ptr1[static_cast<int64_t>(0LL)] = tmp2;
            }
        }
    }
    {
        {
            {
                auto tmp0 = in_ptr1[static_cast<int64_t>(0LL)];
                auto tmp1 = static_cast<int64_t>(1);
                auto tmp2 = decltype(tmp0)(tmp0 + tmp1);
                out_ptr3[static_cast<int64_t>(0LL)] = tmp2;
            }
        }
    }
    {
        {
            {
                auto tmp0 = in_ptr2[static_cast<int64_t>(0LL)];
                auto tmp1 = static_cast<int64_t>(1);
                auto tmp2 = decltype(tmp0)(tmp0 + tmp1);
                out_ptr5[static_cast<int64_t>(0LL)] = tmp2;
            }
        }
    }
    {
        for(int64_t x0=static_cast<int64_t>(0LL); x0<static_cast<int64_t>(32LL); x0+=static_cast<int64_t>(1LL))
        {
            for(int64_t x1=static_cast<int64_t>(0LL); x1<static_cast<int64_t>(16LL); x1+=static_cast<int64_t>(1LL))
            {
                for(int64_t x2=static_cast<int64_t>(0LL); x2<static_cast<int64_t>(9LL); x2+=static_cast<int64_t>(1LL))
                {
                    {
                        {
                            auto tmp0 = in_ptr3[static_cast<int64_t>(x2 + 9LL*x1 + 144LL*x0)];
                            out_ptr6[static_cast<int64_t>(x1 + 16LL*x2 + 144LL*x0)] = tmp0;
                        }
                    }
                }
            }
        }
    }
    {
        for(int64_t x0=static_cast<int64_t>(0LL); x0<static_cast<int64_t>(32LL); x0+=static_cast<int64_t>(1LL))
        {
            for(int64_t x1=static_cast<int64_t>(0LL); x1<static_cast<int64_t>(32LL); x1+=static_cast<int64_t>(1LL))
            {
                for(int64_t x2=static_cast<int64_t>(0LL); x2<static_cast<int64_t>(9LL); x2+=static_cast<int64_t>(1LL))
                {
                    {
                        {
                            auto tmp0 = in_ptr4[static_cast<int64_t>(x2 + 9LL*x1 + 288LL*x0)];
                            out_ptr7[static_cast<int64_t>(x1 + 32LL*x2 + 288LL*x0)] = tmp0;
                        }
                    }
                }
            }
        }
    }
}
''')


cpp_fused__native_batch_norm_legit_functional_max_pool2d_with_indices_relu_1 = async_compile.cpp_pybinding(['const float*', 'const float*', 'const float*', 'const float*', 'const float*', 'float*', 'float*', 'float*', 'float*', 'float*', 'float*', 'float*', 'int8_t*'], '''
#include "./torch_cache/pi/cpicxudqmdsjh5cm4klbtbrvy2cxwr7whxl3md2zzdjdf3orvfdf.h"
extern "C" __declspec(dllexport) void kernel(const float* in_ptr0,
                       const float* in_ptr1,
                       const float* in_ptr2,
                       const float* in_ptr3,
                       const float* in_ptr4,
                       float* out_ptr0,
                       float* out_ptr1,
                       float* out_ptr3,
                       float* out_ptr4,
                       float* out_ptr6,
                       float* out_ptr7,
                       float* out_ptr8,
                       int8_t* out_ptr9)
{
    {
        for(int64_t x0=static_cast<int64_t>(0LL); x0<static_cast<int64_t>(16LL); x0+=static_cast<int64_t>(1LL))
        {
            {
                Welford<float> tmp_acc0 = Welford<float>();
                auto tmp_acc0_arr = std::make_unique<Welford<float>[]>(4);
                for (int i = 0; i < 4; i++)
                {
                    tmp_acc0_arr[i] = Welford<float>();
                }
                #pragma omp parallel num_threads(4)
                {
                    int tid = omp_get_thread_num();
                    Welford<float> tmp_acc0_local = Welford<float>();
                    #pragma omp for
                    for(int64_t x1=static_cast<int64_t>(0LL); x1<static_cast<int64_t>(50176LL); x1+=static_cast<int64_t>(1LL))
                    {
                        {
                            {
                                auto tmp0 = in_ptr0[static_cast<int64_t>(x0 + 16LL*x1)];
                                tmp_acc0_local = welford_combine(tmp_acc0_local, tmp0);
                            }
                        }
                    }
                    tmp_acc0_arr[tid] = tmp_acc0_local;
                }
                for (int tid = 0; tid < 4; tid++)
                {
                    tmp_acc0 = welford_combine(tmp_acc0, tmp_acc0_arr[tid]);
                }
                out_ptr0[static_cast<int64_t>(x0)] = tmp_acc0.mean;
                out_ptr1[static_cast<int64_t>(x0)] = tmp_acc0.m2;
            }
        }
    }
    {
        for(int64_t x0=static_cast<int64_t>(0LL); x0<static_cast<int64_t>(16LL); x0+=static_cast<int64_t>(1LL))
        {
            {
                {
                    auto tmp0 = out_ptr0[static_cast<int64_t>(x0)];
                    auto tmp3 = out_ptr3[static_cast<int64_t>(x0)];
                    auto tmp1 = static_cast<float>(0.1);
                    auto tmp2 = decltype(tmp0)(tmp0 * tmp1);
                    auto tmp4 = static_cast<float>(0.9);
                    auto tmp5 = decltype(tmp3)(tmp3 * tmp4);
                    auto tmp6 = decltype(tmp2)(tmp2 + tmp5);
                    out_ptr3[static_cast<int64_t>(x0)] = tmp6;
                }
            }
        }
    }
    {
        for(int64_t x0=static_cast<int64_t>(0LL); x0<static_cast<int64_t>(16LL); x0+=static_cast<int64_t>(1LL))
        {
            {
                {
                    auto tmp0 = out_ptr1[static_cast<int64_t>(x0)];
                    auto tmp10 = out_ptr6[static_cast<int64_t>(x0)];
                    auto tmp1 = static_cast<float>(50176.0);
                    auto tmp2 = tmp0 / tmp1;
                    auto tmp3 = static_cast<float>(1e-05);
                    auto tmp4 = decltype(tmp2)(tmp2 + tmp3);
                    auto tmp5 = 1 / std::sqrt(tmp4);
                    auto tmp6 = static_cast<float>(1.0000199302441455);
                    auto tmp7 = decltype(tmp2)(tmp2 * tmp6);
                    auto tmp8 = static_cast<float>(0.1);
                    auto tmp9 = decltype(tmp7)(tmp7 * tmp8);
                    auto tmp11 = static_cast<float>(0.9);
                    auto tmp12 = decltype(tmp10)(tmp10 * tmp11);
                    auto tmp13 = decltype(tmp9)(tmp9 + tmp12);
                    out_ptr4[static_cast<int64_t>(x0)] = tmp5;
                    out_ptr6[static_cast<int64_t>(x0)] = tmp13;
                }
            }
        }
    }
    #pragma omp parallel num_threads(4)
    {
        int tid = omp_get_thread_num();
        {
            #pragma omp for
            for(int64_t x0=static_cast<int64_t>(0LL); x0<static_cast<int64_t>(50176LL); x0+=static_cast<int64_t>(1LL))
            {
                for(int64_t x1=static_cast<int64_t>(0LL); x1<static_cast<int64_t>(16LL); x1+=static_cast<int64_t>(1LL))
                {
                    {
                        {
                            auto tmp0 = in_ptr0[static_cast<int64_t>(x1 + 16LL*x0)];
                            auto tmp1 = out_ptr0[static_cast<int64_t>(x1)];
                            auto tmp3 = out_ptr1[static_cast<int64_t>(x1)];
                            auto tmp10 = in_ptr3[static_cast<int64_t>(x1)];
                            auto tmp12 = in_ptr4[static_cast<int64_t>(x1)];
                            auto tmp2 = decltype(tmp0)(tmp0 - tmp1);
                            auto tmp4 = static_cast<float>(50176.0);
                            auto tmp5 = tmp3 / tmp4;
                            auto tmp6 = static_cast<float>(1e-05);
                            auto tmp7 = decltype(tmp5)(tmp5 + tmp6);
                            auto tmp8 = 1 / std::sqrt(tmp7);
                            auto tmp9 = decltype(tmp2)(tmp2 * tmp8);
                            auto tmp11 = decltype(tmp9)(tmp9 * tmp10);
                            auto tmp13 = decltype(tmp11)(tmp11 + tmp12);
                            auto tmp14 = std::max(tmp13, decltype(tmp13)(0));
                            out_ptr7[static_cast<int64_t>(x1 + 16LL*x0)] = tmp14;
                        }
                    }
                }
            }
        }
        {
            #pragma omp for
            for(int64_t x0=static_cast<int64_t>(0LL); x0<static_cast<int64_t>(896LL); x0+=static_cast<int64_t>(1LL))
            {
                for(int64_t x1=static_cast<int64_t>(0LL); x1<static_cast<int64_t>(14LL); x1+=static_cast<int64_t>(1LL))
                {
                    for(int64_t x2=static_cast<int64_t>(0LL); x2<static_cast<int64_t>(16LL); x2+=static_cast<int64_t>(1LL))
                    {
                        {
                            {
                                auto tmp0 = out_ptr7[static_cast<int64_t>(x2 + 32LL*x1 + 896LL*x0)];
                                auto tmp1 = out_ptr7[static_cast<int64_t>(16LL + x2 + 32LL*x1 + 896LL*x0)];
                                auto tmp3 = out_ptr7[static_cast<int64_t>(448LL + x2 + 32LL*x1 + 896LL*x0)];
                                auto tmp5 = out_ptr7[static_cast<int64_t>(464LL + x2 + 32LL*x1 + 896LL*x0)];
                                auto tmp2 = max_propagate_nan(tmp0, tmp1);
                                auto tmp4 = max_propagate_nan(tmp2, tmp3);
                                auto tmp6 = max_propagate_nan(tmp4, tmp5);
                                auto tmp7 = tmp0 > tmp1;
                                auto tmp8 = tmp0 == tmp1;
                                auto tmp9 = tmp0 != tmp0;
                                auto tmp10 = tmp1 != tmp1;
                                auto tmp11 = tmp9 > tmp10;
                                auto tmp12 = tmp7 || tmp11;
                                auto tmp13 = tmp9 && tmp10;
                                auto tmp14 = tmp8 || tmp13;
                                auto tmp15 = static_cast<int64_t>(0);
                                auto tmp16 = static_cast<int64_t>(1);
                                auto tmp17 = tmp15 < tmp16;
                                auto tmp18 = tmp14 && tmp17;
                                auto tmp19 = tmp12 || tmp18;
                                auto tmp20 = tmp19 ? tmp0 : tmp1;
                                auto tmp21 = tmp19 ? tmp15 : tmp16;
                                auto tmp22 = tmp20 > tmp3;
                                auto tmp23 = tmp20 == tmp3;
                                auto tmp24 = tmp20 != tmp20;
                                auto tmp25 = tmp3 != tmp3;
                                auto tmp26 = tmp24 > tmp25;
                                auto tmp27 = tmp22 || tmp26;
                                auto tmp28 = tmp24 && tmp25;
                                auto tmp29 = tmp23 || tmp28;
                                auto tmp30 = static_cast<int64_t>(2);
                                auto tmp31 = tmp21 < tmp30;
                                auto tmp32 = tmp29 && tmp31;
                                auto tmp33 = tmp27 || tmp32;
                                auto tmp34 = tmp33 ? tmp20 : tmp3;
                                auto tmp35 = tmp33 ? tmp21 : tmp30;
                                auto tmp36 = tmp34 > tmp5;
                                auto tmp37 = tmp34 == tmp5;
                                auto tmp38 = tmp34 != tmp34;
                                auto tmp39 = tmp5 != tmp5;
                                auto tmp40 = tmp38 > tmp39;
                                auto tmp41 = tmp36 || tmp40;
                                auto tmp42 = tmp38 && tmp39;
                                auto tmp43 = tmp37 || tmp42;
                                auto tmp44 = static_cast<int64_t>(3);
                                auto tmp45 = tmp35 < tmp44;
                                auto tmp46 = tmp43 && tmp45;
                                auto tmp47 = tmp41 || tmp46;
                                auto tmp48 = tmp47 ? tmp34 : tmp5;
                                auto tmp49 = tmp47 ? tmp35 : tmp44;
                                auto tmp50 = c10::convert<int8_t>(tmp49);
                                out_ptr8[static_cast<int64_t>(x2 + 16LL*x1 + 224LL*x0)] = tmp6;
                                out_ptr9[static_cast<int64_t>(x2 + 16LL*x1 + 224LL*x0)] = tmp50;
                            }
                        }
                    }
                }
            }
        }
    }
}
''')


cpp_fused__native_batch_norm_legit_functional_max_pool2d_with_indices_relu_2 = async_compile.cpp_pybinding(['const float*', 'const float*', 'const float*', 'const float*', 'const float*', 'float*', 'float*', 'float*', 'float*', 'float*', 'float*', 'float*', 'int8_t*'], '''
#include "./torch_cache/pi/cpicxudqmdsjh5cm4klbtbrvy2cxwr7whxl3md2zzdjdf3orvfdf.h"
extern "C" __declspec(dllexport) void kernel(const float* in_ptr0,
                       const float* in_ptr1,
                       const float* in_ptr2,
                       const float* in_ptr3,
                       const float* in_ptr4,
                       float* out_ptr0,
                       float* out_ptr1,
                       float* out_ptr3,
                       float* out_ptr4,
                       float* out_ptr6,
                       float* out_ptr7,
                       float* out_ptr8,
                       int8_t* out_ptr9)
{
    {
        for(int64_t x0=static_cast<int64_t>(0LL); x0<static_cast<int64_t>(32LL); x0+=static_cast<int64_t>(1LL))
        {
            {
                Welford<float> tmp_acc0 = Welford<float>();
                auto tmp_acc0_arr = std::make_unique<Welford<float>[]>(4);
                for (int i = 0; i < 4; i++)
                {
                    tmp_acc0_arr[i] = Welford<float>();
                }
                #pragma omp parallel num_threads(4)
                {
                    int tid = omp_get_thread_num();
                    Welford<float> tmp_acc0_local = Welford<float>();
                    #pragma omp for
                    for(int64_t x1=static_cast<int64_t>(0LL); x1<static_cast<int64_t>(12544LL); x1+=static_cast<int64_t>(1LL))
                    {
                        {
                            {
                                auto tmp0 = in_ptr0[static_cast<int64_t>(x0 + 32LL*x1)];
                                tmp_acc0_local = welford_combine(tmp_acc0_local, tmp0);
                            }
                        }
                    }
                    tmp_acc0_arr[tid] = tmp_acc0_local;
                }
                for (int tid = 0; tid < 4; tid++)
                {
                    tmp_acc0 = welford_combine(tmp_acc0, tmp_acc0_arr[tid]);
                }
                out_ptr0[static_cast<int64_t>(x0)] = tmp_acc0.mean;
                out_ptr1[static_cast<int64_t>(x0)] = tmp_acc0.m2;
            }
        }
    }
    {
        for(int64_t x0=static_cast<int64_t>(0LL); x0<static_cast<int64_t>(32LL); x0+=static_cast<int64_t>(1LL))
        {
            {
                {
                    auto tmp0 = out_ptr0[static_cast<int64_t>(x0)];
                    auto tmp3 = out_ptr3[static_cast<int64_t>(x0)];
                    auto tmp1 = static_cast<float>(0.1);
                    auto tmp2 = decltype(tmp0)(tmp0 * tmp1);
                    auto tmp4 = static_cast<float>(0.9);
                    auto tmp5 = decltype(tmp3)(tmp3 * tmp4);
                    auto tmp6 = decltype(tmp2)(tmp2 + tmp5);
                    out_ptr3[static_cast<int64_t>(x0)] = tmp6;
                }
            }
        }
    }
    {
        for(int64_t x0=static_cast<int64_t>(0LL); x0<static_cast<int64_t>(32LL); x0+=static_cast<int64_t>(1LL))
        {
            {
                {
                    auto tmp0 = out_ptr1[static_cast<int64_t>(x0)];
                    auto tmp10 = out_ptr6[static_cast<int64_t>(x0)];
                    auto tmp1 = static_cast<float>(12544.0);
                    auto tmp2 = tmp0 / tmp1;
                    auto tmp3 = static_cast<float>(1e-05);
                    auto tmp4 = decltype(tmp2)(tmp2 + tmp3);
                    auto tmp5 = 1 / std::sqrt(tmp4);
                    auto tmp6 = static_cast<float>(1.0000797257434426);
                    auto tmp7 = decltype(tmp2)(tmp2 * tmp6);
                    auto tmp8 = static_cast<float>(0.1);
                    auto tmp9 = decltype(tmp7)(tmp7 * tmp8);
                    auto tmp11 = static_cast<float>(0.9);
                    auto tmp12 = decltype(tmp10)(tmp10 * tmp11);
                    auto tmp13 = decltype(tmp9)(tmp9 + tmp12);
                    out_ptr4[static_cast<int64_t>(x0)] = tmp5;
                    out_ptr6[static_cast<int64_t>(x0)] = tmp13;
                }
            }
        }
    }
    #pragma omp parallel num_threads(4)
    {
        int tid = omp_get_thread_num();
        {
            #pragma omp for
            for(int64_t x0=static_cast<int64_t>(0LL); x0<static_cast<int64_t>(12544LL); x0+=static_cast<int64_t>(1LL))
            {
                for(int64_t x1=static_cast<int64_t>(0LL); x1<static_cast<int64_t>(32LL); x1+=static_cast<int64_t>(1LL))
                {
                    {
                        {
                            auto tmp0 = in_ptr0[static_cast<int64_t>(x1 + 32LL*x0)];
                            auto tmp1 = out_ptr0[static_cast<int64_t>(x1)];
                            auto tmp3 = out_ptr1[static_cast<int64_t>(x1)];
                            auto tmp10 = in_ptr3[static_cast<int64_t>(x1)];
                            auto tmp12 = in_ptr4[static_cast<int64_t>(x1)];
                            auto tmp2 = decltype(tmp0)(tmp0 - tmp1);
                            auto tmp4 = static_cast<float>(12544.0);
                            auto tmp5 = tmp3 / tmp4;
                            auto tmp6 = static_cast<float>(1e-05);
                            auto tmp7 = decltype(tmp5)(tmp5 + tmp6);
                            auto tmp8 = 1 / std::sqrt(tmp7);
                            auto tmp9 = decltype(tmp2)(tmp2 * tmp8);
                            auto tmp11 = decltype(tmp9)(tmp9 * tmp10);
                            auto tmp13 = decltype(tmp11)(tmp11 + tmp12);
                            auto tmp14 = std::max(tmp13, decltype(tmp13)(0));
                            out_ptr7[static_cast<int64_t>(x1 + 32LL*x0)] = tmp14;
                        }
                    }
                }
            }
        }
        {
            #pragma omp for
            for(int64_t x0=static_cast<int64_t>(0LL); x0<static_cast<int64_t>(448LL); x0+=static_cast<int64_t>(1LL))
            {
                for(int64_t x1=static_cast<int64_t>(0LL); x1<static_cast<int64_t>(7LL); x1+=static_cast<int64_t>(1LL))
                {
                    for(int64_t x2=static_cast<int64_t>(0LL); x2<static_cast<int64_t>(32LL); x2+=static_cast<int64_t>(1LL))
                    {
                        {
                            {
                                auto tmp0 = out_ptr7[static_cast<int64_t>(x2 + 64LL*x1 + 896LL*x0)];
                                auto tmp1 = out_ptr7[static_cast<int64_t>(32LL + x2 + 64LL*x1 + 896LL*x0)];
                                auto tmp3 = out_ptr7[static_cast<int64_t>(448LL + x2 + 64LL*x1 + 896LL*x0)];
                                auto tmp5 = out_ptr7[static_cast<int64_t>(480LL + x2 + 64LL*x1 + 896LL*x0)];
                                auto tmp2 = max_propagate_nan(tmp0, tmp1);
                                auto tmp4 = max_propagate_nan(tmp2, tmp3);
                                auto tmp6 = max_propagate_nan(tmp4, tmp5);
                                auto tmp7 = tmp0 > tmp1;
                                auto tmp8 = tmp0 == tmp1;
                                auto tmp9 = tmp0 != tmp0;
                                auto tmp10 = tmp1 != tmp1;
                                auto tmp11 = tmp9 > tmp10;
                                auto tmp12 = tmp7 || tmp11;
                                auto tmp13 = tmp9 && tmp10;
                                auto tmp14 = tmp8 || tmp13;
                                auto tmp15 = static_cast<int64_t>(0);
                                auto tmp16 = static_cast<int64_t>(1);
                                auto tmp17 = tmp15 < tmp16;
                                auto tmp18 = tmp14 && tmp17;
                                auto tmp19 = tmp12 || tmp18;
                                auto tmp20 = tmp19 ? tmp0 : tmp1;
                                auto tmp21 = tmp19 ? tmp15 : tmp16;
                                auto tmp22 = tmp20 > tmp3;
                                auto tmp23 = tmp20 == tmp3;
                                auto tmp24 = tmp20 != tmp20;
                                auto tmp25 = tmp3 != tmp3;
                                auto tmp26 = tmp24 > tmp25;
                                auto tmp27 = tmp22 || tmp26;
                                auto tmp28 = tmp24 && tmp25;
                                auto tmp29 = tmp23 || tmp28;
                                auto tmp30 = static_cast<int64_t>(2);
                                auto tmp31 = tmp21 < tmp30;
                                auto tmp32 = tmp29 && tmp31;
                                auto tmp33 = tmp27 || tmp32;
                                auto tmp34 = tmp33 ? tmp20 : tmp3;
                                auto tmp35 = tmp33 ? tmp21 : tmp30;
                                auto tmp36 = tmp34 > tmp5;
                                auto tmp37 = tmp34 == tmp5;
                                auto tmp38 = tmp34 != tmp34;
                                auto tmp39 = tmp5 != tmp5;
                                auto tmp40 = tmp38 > tmp39;
                                auto tmp41 = tmp36 || tmp40;
                                auto tmp42 = tmp38 && tmp39;
                                auto tmp43 = tmp37 || tmp42;
                                auto tmp44 = static_cast<int64_t>(3);
                                auto tmp45 = tmp35 < tmp44;
                                auto tmp46 = tmp43 && tmp45;
                                auto tmp47 = tmp41 || tmp46;
                                auto tmp48 = tmp47 ? tmp34 : tmp5;
                                auto tmp49 = tmp47 ? tmp35 : tmp44;
                                auto tmp50 = c10::convert<int8_t>(tmp49);
                                out_ptr8[static_cast<int64_t>(x2 + 32LL*x1 + 224LL*x0)] = tmp6;
                                out_ptr9[static_cast<int64_t>(x2 + 32LL*x1 + 224LL*x0)] = tmp50;
                            }
                        }
                    }
                }
            }
        }
    }
}
''')


cpp_fused__native_batch_norm_legit_functional_relu_threshold_backward_3 = async_compile.cpp_pybinding(['const float*', 'const float*', 'const float*', 'const float*', 'const float*', 'float*', 'float*', 'float*', 'float*', 'float*', 'float*', 'bool*'], '''
#include "./torch_cache/pi/cpicxudqmdsjh5cm4klbtbrvy2cxwr7whxl3md2zzdjdf3orvfdf.h"
extern "C" __declspec(dllexport) void kernel(const float* in_ptr0,
                       const float* in_ptr1,
                       const float* in_ptr2,
                       const float* in_ptr3,
                       const float* in_ptr4,
                       float* out_ptr0,
                       float* out_ptr1,
                       float* out_ptr3,
                       float* out_ptr4,
                       float* out_ptr6,
                       float* out_ptr7,
                       bool* out_ptr8)
{
    #pragma omp parallel num_threads(4)
    {
        int tid = omp_get_thread_num();
        {
            #pragma omp for
            for(int64_t x0=static_cast<int64_t>(0LL); x0<static_cast<int64_t>(32LL); x0+=static_cast<int64_t>(1LL))
            {
                {
                    Welford<float> tmp_acc0 = Welford<float>();
                    for(int64_t x1=static_cast<int64_t>(0LL); x1<static_cast<int64_t>(3136LL); x1+=static_cast<int64_t>(1LL))
                    {
                        {
                            {
                                auto tmp0 = in_ptr0[static_cast<int64_t>(x0 + 32LL*x1)];
                                tmp_acc0 = welford_combine(tmp_acc0, tmp0);
                            }
                        }
                    }
                    out_ptr0[static_cast<int64_t>(x0)] = tmp_acc0.mean;
                    out_ptr1[static_cast<int64_t>(x0)] = tmp_acc0.m2;
                }
                {
                    {
                        auto tmp0 = out_ptr0[static_cast<int64_t>(x0)];
                        auto tmp3 = in_ptr1[static_cast<int64_t>(x0)];
                        auto tmp1 = static_cast<float>(0.1);
                        auto tmp2 = decltype(tmp0)(tmp0 * tmp1);
                        auto tmp4 = static_cast<float>(0.9);
                        auto tmp5 = decltype(tmp3)(tmp3 * tmp4);
                        auto tmp6 = decltype(tmp2)(tmp2 + tmp5);
                        out_ptr3[static_cast<int64_t>(x0)] = tmp6;
                    }
                }
                {
                    {
                        auto tmp0 = out_ptr1[static_cast<int64_t>(x0)];
                        auto tmp10 = in_ptr2[static_cast<int64_t>(x0)];
                        auto tmp1 = static_cast<float>(3136.0);
                        auto tmp2 = tmp0 / tmp1;
                        auto tmp3 = static_cast<float>(1e-05);
                        auto tmp4 = decltype(tmp2)(tmp2 + tmp3);
                        auto tmp5 = 1 / std::sqrt(tmp4);
                        auto tmp6 = static_cast<float>(1.0003189792663476);
                        auto tmp7 = decltype(tmp2)(tmp2 * tmp6);
                        auto tmp8 = static_cast<float>(0.1);
                        auto tmp9 = decltype(tmp7)(tmp7 * tmp8);
                        auto tmp11 = static_cast<float>(0.9);
                        auto tmp12 = decltype(tmp10)(tmp10 * tmp11);
                        auto tmp13 = decltype(tmp9)(tmp9 + tmp12);
                        out_ptr4[static_cast<int64_t>(x0)] = tmp5;
                        out_ptr6[static_cast<int64_t>(x0)] = tmp13;
                    }
                }
            }
        }
        {
            #pragma omp for
            for(int64_t x0=static_cast<int64_t>(0LL); x0<static_cast<int64_t>(64LL); x0+=static_cast<int64_t>(1LL))
            {
                for(int64_t x1=static_cast<int64_t>(0LL); x1<static_cast<int64_t>(32LL); x1+=static_cast<int64_t>(1LL))
                {
                    for(int64_t x2=static_cast<int64_t>(0LL); x2<static_cast<int64_t>(49LL); x2+=static_cast<int64_t>(1LL))
                    {
                        {
                            {
                                auto tmp0 = in_ptr0[static_cast<int64_t>(x1 + 32LL*x2 + 1568LL*x0)];
                                auto tmp1 = out_ptr0[static_cast<int64_t>(x1)];
                                auto tmp3 = out_ptr1[static_cast<int64_t>(x1)];
                                auto tmp10 = in_ptr3[static_cast<int64_t>(x1)];
                                auto tmp12 = in_ptr4[static_cast<int64_t>(x1)];
                                auto tmp2 = decltype(tmp0)(tmp0 - tmp1);
                                auto tmp4 = static_cast<float>(3136.0);
                                auto tmp5 = tmp3 / tmp4;
                                auto tmp6 = static_cast<float>(1e-05);
                                auto tmp7 = decltype(tmp5)(tmp5 + tmp6);
                                auto tmp8 = 1 / std::sqrt(tmp7);
                                auto tmp9 = decltype(tmp2)(tmp2 * tmp8);
                                auto tmp11 = decltype(tmp9)(tmp9 * tmp10);
                                auto tmp13 = decltype(tmp11)(tmp11 + tmp12);
                                auto tmp14 = std::max(tmp13, decltype(tmp13)(0));
                                auto tmp15 = static_cast<float>(0.0);
                                auto tmp16 = tmp14 <= tmp15;
                                out_ptr7[static_cast<int64_t>(x2 + 49LL*x1 + 1568LL*x0)] = tmp14;
                                out_ptr8[static_cast<int64_t>(x1 + 32LL*x2 + 1568LL*x0)] = tmp16;
                            }
                        }
                    }
                }
            }
        }
    }
}
''')


async_compile.wait(globals())
del async_compile

def call(args):
    primals_1, primals_2, primals_3, primals_4, primals_5, primals_6, primals_7, primals_8, primals_9, primals_10, primals_11, primals_12, primals_13, primals_14, primals_15, primals_16, primals_17, primals_18, primals_19, primals_20, primals_21, primals_22, primals_23, primals_24 = args
    args.clear()
    assert_size_stride(primals_1, (16, 1, 3, 3), (9, 9, 3, 1))
    assert_size_stride(primals_2, (16, ), (1, ))
    assert_size_stride(primals_3, (64, 1, 28, 28), (784, 784, 28, 1))
    assert_size_stride(primals_4, (), ())
    assert_size_stride(primals_5, (16, ), (1, ))
    assert_size_stride(primals_6, (16, ), (1, ))
    assert_size_stride(primals_7, (16, ), (1, ))
    assert_size_stride(primals_8, (16, ), (1, ))
    assert_size_stride(primals_9, (32, 16, 3, 3), (144, 9, 3, 1))
    assert_size_stride(primals_10, (32, ), (1, ))
    assert_size_stride(primals_11, (), ())
    assert_size_stride(primals_12, (32, ), (1, ))
    assert_size_stride(primals_13, (32, ), (1, ))
    assert_size_stride(primals_14, (32, ), (1, ))
    assert_size_stride(primals_15, (32, ), (1, ))
    assert_size_stride(primals_16, (32, 32, 3, 3), (288, 9, 3, 1))
    assert_size_stride(primals_17, (32, ), (1, ))
    assert_size_stride(primals_18, (), ())
    assert_size_stride(primals_19, (32, ), (1, ))
    assert_size_stride(primals_20, (32, ), (1, ))
    assert_size_stride(primals_21, (32, ), (1, ))
    assert_size_stride(primals_22, (32, ), (1, ))
    assert_size_stride(primals_23, (10, 1568), (1568, 1))
    assert_size_stride(primals_24, (10, ), (1, ))
    buf0 = empty_strided_cpu((32, 16, 3, 3), (144, 1, 48, 16), torch.float32)
    buf1 = empty_strided_cpu((32, 32, 3, 3), (288, 1, 96, 32), torch.float32)
    cpp_fused_add_0(primals_4, primals_11, primals_18, primals_9, primals_16, primals_4, primals_11, primals_18, buf0, buf1)
    del primals_11
    del primals_16
    del primals_18
    del primals_4
    del primals_9
    # Topologically Sorted Source Nodes: [input_1], Original ATen: [aten.convolution]
    buf2 = extern_kernels.convolution(reinterpret_tensor(primals_3, (64, 1, 28, 28), (784, 1, 28, 1), 0), reinterpret_tensor(primals_1, (16, 1, 3, 3), (9, 1, 3, 1), 0), primals_2, stride=(1, 1), padding=(1, 1), dilation=(1, 1), transposed=False, output_padding=(0, 0), groups=1)
    assert_size_stride(buf2, (64, 16, 28, 28), (12544, 1, 448, 16))
    del primals_2
    buf3 = empty_strided_cpu((1, 16, 1, 1), (16, 1, 16, 16), torch.float32)
    buf4 = empty_strided_cpu((1, 16, 1, 1), (16, 1, 16, 16), torch.float32)
    buf6 = empty_strided_cpu((1, 16, 1, 1), (16, 1, 16, 16), torch.float32)
    buf7 = empty_strided_cpu((64, 16, 28, 28), (12544, 1, 448, 16), torch.float32)
    buf8 = empty_strided_cpu((64, 16, 14, 14), (3136, 1, 224, 16), torch.float32)
    buf9 = empty_strided_cpu((64, 16, 14, 14), (3136, 1, 224, 16), torch.int8)
    cpp_fused__native_batch_norm_legit_functional_max_pool2d_with_indices_relu_1(buf2, primals_5, primals_6, primals_7, primals_8, buf3, buf4, primals_5, buf6, primals_6, buf7, buf8, buf9)
    del buf4
    del primals_5
    del primals_6
    del primals_8
    # Topologically Sorted Source Nodes: [input_5], Original ATen: [aten.convolution]
    buf10 = extern_kernels.convolution(buf8, buf0, primals_10, stride=(1, 1), padding=(1, 1), dilation=(1, 1), transposed=False, output_padding=(0, 0), groups=1)
    assert_size_stride(buf10, (64, 32, 14, 14), (6272, 1, 448, 32))
    del primals_10
    buf11 = empty_strided_cpu((1, 32, 1, 1), (32, 1, 32, 32), torch.float32)
    buf12 = empty_strided_cpu((1, 32, 1, 1), (32, 1, 32, 32), torch.float32)
    buf14 = empty_strided_cpu((1, 32, 1, 1), (32, 1, 32, 32), torch.float32)
    buf15 = empty_strided_cpu((64, 32, 14, 14), (6272, 1, 448, 32), torch.float32)
    buf16 = empty_strided_cpu((64, 32, 7, 7), (1568, 1, 224, 32), torch.float32)
    buf17 = empty_strided_cpu((64, 32, 7, 7), (1568, 1, 224, 32), torch.int8)
    cpp_fused__native_batch_norm_legit_functional_max_pool2d_with_indices_relu_2(buf10, primals_12, primals_13, primals_14, primals_15, buf11, buf12, primals_12, buf14, primals_13, buf15, buf16, buf17)
    del primals_12
    del primals_13
    del primals_15
    # Topologically Sorted Source Nodes: [input_9], Original ATen: [aten.convolution]
    buf18 = extern_kernels.convolution(buf16, buf1, primals_17, stride=(1, 1), padding=(1, 1), dilation=(1, 1), transposed=False, output_padding=(0, 0), groups=1)
    assert_size_stride(buf18, (64, 32, 7, 7), (1568, 1, 224, 32))
    del primals_17
    buf19 = buf12; del buf12  # reuse
    buf20 = empty_strided_cpu((1, 32, 1, 1), (32, 1, 32, 32), torch.float32)
    buf22 = empty_strided_cpu((1, 32, 1, 1), (32, 1, 32, 32), torch.float32)
    buf23 = empty_strided_cpu((64, 32, 7, 7), (1568, 49, 7, 1), torch.float32)
    buf25 = empty_strided_cpu((64, 32, 7, 7), (1568, 1, 224, 32), torch.bool)
    cpp_fused__native_batch_norm_legit_functional_relu_threshold_backward_3(buf18, primals_19, primals_20, primals_21, primals_22, buf19, buf20, primals_19, buf22, primals_20, buf23, buf25)
    del buf20
    del primals_19
    del primals_20
    del primals_22
    buf24 = empty_strided_cpu((64, 10), (10, 1), torch.float32)
    # Topologically Sorted Source Nodes: [x_1], Original ATen: [aten.addmm]
    extern_kernels.addmm(primals_24, reinterpret_tensor(buf23, (64, 1568), (1568, 1), 0), reinterpret_tensor(primals_23, (1568, 10), (1, 1568), 0), alpha=1, beta=1, out=buf24)
    del primals_24
    return (buf24, primals_1, primals_3, primals_7, buf0, primals_14, buf1, primals_21, buf2, reinterpret_tensor(buf6, (16, ), (1, ), 0), buf7, buf8, buf9, buf10, reinterpret_tensor(buf14, (32, ), (1, ), 0), buf15, buf16, buf17, buf18, reinterpret_tensor(buf22, (32, ), (1, ), 0), reinterpret_tensor(buf23, (64, 1568), (1568, 1), 0), primals_23, buf25, reinterpret_tensor(buf19, (1, 32, 1, 1), (32, 1, 1, 1), 0), reinterpret_tensor(buf11, (1, 32, 1, 1), (32, 1, 1, 1), 0), reinterpret_tensor(buf3, (1, 16, 1, 1), (16, 1, 1, 1), 0), )


def benchmark_compiled_module(times=10, repeat=10):
    from torch._dynamo.testing import rand_strided
    from torch._inductor.utils import print_performance
    primals_1 = rand_strided((16, 1, 3, 3), (9, 9, 3, 1), device='cpu', dtype=torch.float32)
    primals_2 = rand_strided((16, ), (1, ), device='cpu', dtype=torch.float32)
    primals_3 = rand_strided((64, 1, 28, 28), (784, 784, 28, 1), device='cpu', dtype=torch.float32)
    primals_4 = rand_strided((), (), device='cpu', dtype=torch.int64)
    primals_5 = rand_strided((16, ), (1, ), device='cpu', dtype=torch.float32)
    primals_6 = rand_strided((16, ), (1, ), device='cpu', dtype=torch.float32)
    primals_7 = rand_strided((16, ), (1, ), device='cpu', dtype=torch.float32)
    primals_8 = rand_strided((16, ), (1, ), device='cpu', dtype=torch.float32)
    primals_9 = rand_strided((32, 16, 3, 3), (144, 9, 3, 1), device='cpu', dtype=torch.float32)
    primals_10 = rand_strided((32, ), (1, ), device='cpu', dtype=torch.float32)
    primals_11 = rand_strided((), (), device='cpu', dtype=torch.int64)
    primals_12 = rand_strided((32, ), (1, ), device='cpu', dtype=torch.float32)
    primals_13 = rand_strided((32, ), (1, ), device='cpu', dtype=torch.float32)
    primals_14 = rand_strided((32, ), (1, ), device='cpu', dtype=torch.float32)
    primals_15 = rand_strided((32, ), (1, ), device='cpu', dtype=torch.float32)
    primals_16 = rand_strided((32, 32, 3, 3), (288, 9, 3, 1), device='cpu', dtype=torch.float32)
    primals_17 = rand_strided((32, ), (1, ), device='cpu', dtype=torch.float32)
    primals_18 = rand_strided((), (), device='cpu', dtype=torch.int64)
    primals_19 = rand_strided((32, ), (1, ), device='cpu', dtype=torch.float32)
    primals_20 = rand_strided((32, ), (1, ), device='cpu', dtype=torch.float32)
    primals_21 = rand_strided((32, ), (1, ), device='cpu', dtype=torch.float32)
    primals_22 = rand_strided((32, ), (1, ), device='cpu', dtype=torch.float32)
    primals_23 = rand_strided((10, 1568), (1568, 1), device='cpu', dtype=torch.float32)
    primals_24 = rand_strided((10, ), (1, ), device='cpu', dtype=torch.float32)
    fn = lambda: call([primals_1, primals_2, primals_3, primals_4, primals_5, primals_6, primals_7, primals_8, primals_9, primals_10, primals_11, primals_12, primals_13, primals_14, primals_15, primals_16, primals_17, primals_18, primals_19, primals_20, primals_21, primals_22, primals_23, primals_24])
    return print_performance(fn, times=times, repeat=repeat)


if __name__ == "__main__":
    from torch._inductor.wrapper_benchmark import compiled_module_main
    compiled_module_main('None', benchmark_compiled_module)
