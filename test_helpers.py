"""Shared test helpers and fixtures for MNIST and CNN tests."""

from torchvision import transforms, datasets
from torch.utils.data import DataLoader


def get_mnist_dataloaders(
    data_dir: str = "./mnist_data", batch_size: int = 64
) -> tuple:
    """
    Download and load the MNIST dataset, returning train and test DataLoaders.

    Args:
        data_dir (str): Directory to store/download the MNIST data.
        batch_size (int): Batch size for the DataLoaders.

    Returns:
        train_loader (DataLoader): DataLoader object for training.
        test_loader (DataLoader): DataLoader object for testing.
    """
    transform = transforms.Compose(
        [
            transforms.ToTensor(),
        ]
    )

    train_dataset = datasets.MNIST(
        root=data_dir, train=True, download=True, transform=transform
    )

    test_dataset = datasets.MNIST(
        root=data_dir, train=False, download=True, transform=transform
    )

    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)

    return train_loader, test_loader
