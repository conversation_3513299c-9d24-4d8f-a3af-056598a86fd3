"""Validation script for MNIST models (MLP and CNN) with checkpointing and CLI support."""
import logging
import os

import torch
from torch.utils.data import DataLoader, Subset
from torchvision import transforms

# Local imports
from mnist_data_helpers import PatchedMNIST, get_full_test_loader
from mnist_cli_helpers import parse_and_load_model_with_checkpoint, load_and_prepare_model

# All imports should be at the top of the file, not inside functions or classes.
# Configure logging
LOG_LEVEL = os.environ.get("MNIST_LOG_LEVEL", "INFO").upper()
logging.basicConfig(
    format="%(asctime)s [%(levelname)s] %(message)s",
    level=getattr(logging, LOG_LEVEL, logging.INFO),
)
logger = logging.getLogger("mnist_validate")


def validate_main(args=None) -> None:  # pylint: disable=too-many-locals
    """
    Main validation script for MNIST models.

    Args:
        args (argparse.Namespace): Command line arguments. If None, parses arguments.
    Too many locals warning is suppressed for CLI clarity and maintainability.
    """
    if args is None:
        args, _, _, _, _ = parse_and_load_model_with_checkpoint(
            description="MNIST validation script.", default_batch_size=64, default_test_mode=False
        )
    model, _, _, start_epoch = load_and_prepare_model(args, logger)
    if start_epoch is None:
        return
    if getattr(args, 'test_mode', False):
        transform = transforms.Compose([
            transforms.ToTensor(),
        ])
        test_dataset = PatchedMNIST(
            root=args.data_dir, train=False, download=True, transform=transform
        )
        test_dataset = Subset(test_dataset, range(8))
        test_loader = DataLoader(
            test_dataset, batch_size=args.batch_size, shuffle=False
        )
    else:
        test_loader = get_full_test_loader(
            batch_size=args.batch_size, data_dir=args.data_dir
        )
    correct = 0
    total = 0
    logger.info("Running validation on the MNIST test set...")
    with torch.no_grad():
        for images, labels in test_loader:
            outputs = model(images)
            preds = outputs.argmax(dim=1)
            correct += (preds == labels).sum().item()
            total += labels.size(0)
    accuracy = correct / total if total > 0 else 0.0
    logger.info("Validation accuracy on %d test samples: %.4f", total, accuracy)


if __name__ == "__main__":
    try:
        validate_main()
    except KeyboardInterrupt:
        logger.warning("Validation interrupted by user.")
    except Exception as e:  # pylint: disable=broad-exception-caught
        # Catching all exceptions to ensure uncaught errors are logged in CLI usage.
        logger.error("Uncaught exception: %s", e, exc_info=True)
        print(f"[ERROR] {e}")
