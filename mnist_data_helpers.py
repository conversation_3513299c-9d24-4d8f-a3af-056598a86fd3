"""Shared data helpers for MNIST scripts: PatchedMNIST and dataloader utilities."""
from PIL import Image
from torch.utils.data import DataLoader, Subset
from torchvision import datasets, transforms

class PatchedMNIST(datasets.MNIST):
    """Patched MNIST dataset to ensure compatibility with PIL transforms."""
    def __getitem__(self, index):
        img, target = self.data[index], int(self.targets[index])
        img = Image.fromarray(img.numpy()).convert("L")
        if self.transform is not None:
            img = self.transform(img)
        if self.target_transform is not None:
            target = self.target_transform(target)
        return img, target

def get_mnist_dataloaders(data_dir: str = "./mnist_data", batch_size: int = 64):
    """Download and load the MNIST dataset, returning train and test DataLoaders."""
    transform = transforms.Compose([
        transforms.ToTensor(),
    ])
    train_dataset = PatchedMNIST(
        root=data_dir, train=True, download=True, transform=transform
    )
    test_dataset = PatchedMNIST(
        root=data_dir, train=False, download=True, transform=transform
    )
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    return train_loader, test_loader

def get_full_test_loader(batch_size: int = 64, data_dir: str = "./mnist_data"):
    """Creates a DataLoader for the full MNIST test dataset."""
    transform = transforms.Compose([
        transforms.ToTensor(),
    ])
    test_dataset = PatchedMNIST(
        root=data_dir, train=False, download=True, transform=transform
    )
    loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    return loader

def get_small_mnist_loaders(batch_size: int = 8, num_samples: int = 100) -> tuple:
    """
    Get small MNIST DataLoaders for testing.
    Args:
        batch_size (int): Batch size for loaders.
        num_samples (int): Number of samples for train/test.
    Returns:
        tuple: (train_loader, test_loader)
    """
    transform = transforms.Compose([
        transforms.ToTensor(),
    ])
    train_dataset = PatchedMNIST(
        root="./mnist_data", train=True, download=True, transform=transform
    )
    test_dataset = PatchedMNIST(
        root="./mnist_data", train=False, download=True, transform=transform
    )
    train_loader = DataLoader(
        Subset(train_dataset, range(num_samples)), batch_size=batch_size, shuffle=True
    )
    test_loader = DataLoader(
        Subset(test_dataset, range(num_samples)), batch_size=batch_size, shuffle=False
    )
    return train_loader, test_loader
