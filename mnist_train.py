"""Training script for MNIST models (MLP and CNN) with checkpointing and CLI support.

This script provides training routines, checkpointing, and CLI for both MLP and CNN models
on MNIST.
"""

import logging
import os
import time
from datetime import datetime
import argparse

# Third-party imports
import torch
from torch import nn, optim
from torch.utils.data import DataLoader, Subset
from torchvision import transforms

# Local imports
from mnist_data_helpers import PatchedMNIST, get_mnist_dataloaders
from mnist_cli_helpers import add_common_arguments, get_model_from_args
from mnist_checkpoint_helpers import get_model_config, load_latest_checkpoint

# Configure logging
LOG_LEVEL = os.environ.get("MNIST_LOG_LEVEL", "INFO").upper()
logging.basicConfig(
    format="%(asctime)s [%(levelname)s] %(message)s",
    level=getattr(logging, LOG_LEVEL, logging.INFO),
)
logger = logging.getLogger("mnist_train")


def save_checkpoint(
    model, optimizer, epoch, checkpoint_dir="./checkpoints", tag=None, model_type="mlp"
):  # pylint: disable=too-many-arguments,too-many-positional-arguments
    """
    Save model and optimizer state as a checkpoint.
    Too many arguments/positional-arguments disables are justified for flexibility in CLI usage.
    """
    os.makedirs(checkpoint_dir, exist_ok=True)
    if tag is None:
        checkpoint_path = os.path.join(
            checkpoint_dir, f"mnist_{model_type}_epoch_{epoch}.pt"
        )
    else:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        checkpoint_path = os.path.join(
            checkpoint_dir, f"mnist_{model_type}_epoch_{epoch}_{tag}_{timestamp}.pt"
        )
    torch.save(
        {
            "epoch": epoch,
            "model_state_dict": model.state_dict(),
            "optimizer_state_dict": optimizer.state_dict(),
            "model_config": get_model_config(model, model_type),
        },
        checkpoint_path,
    )
    logger.info("Checkpoint saved: %s", checkpoint_path)


def train_model(
    model,
    train_loader,
    test_loader,
    epochs=3,
    lr=0.01,
    device=None,
    checkpoint_dir="./checkpoints",
    save_every_minutes=None,
    model_type="mlp",
):  # pylint: disable=too-many-arguments,too-many-locals,too-many-statements,too-many-positional-arguments
    """
    Train the model on the MNIST dataset.
    Args:
        model: The neural network to train.
        train_loader: DataLoader for training data.
        test_loader: DataLoader for test data.
        epochs (int): Number of training epochs.
        lr (float): Learning rate.
        device: torch.device to use (CPU by default).
        checkpoint_dir (str): Directory to save checkpoints.
        save_every_minutes (float or None): If set, save a checkpoint every n minutes
            during training.
        model_type (str): Model type string for filename (e.g., 'mlp', 'cnn').
    """
    if device is None:
        device = torch.device("cpu")
    model.to(device)
    optimizer = optim.SGD(model.parameters(), lr=lr)
    # Load latest checkpoint if available
    start_epoch = load_latest_checkpoint(
        model, optimizer, checkpoint_dir, model_type=model_type
    )
    criterion = nn.CrossEntropyLoss()
    last_save_time = time.time()
    for epoch in range(start_epoch, epochs + 1):
        model.train()
        running_loss = 0.0
        correct = 0
        total = 0
        for batch_idx, (images, labels) in enumerate(train_loader):
            images, labels = images.to(device), labels.to(device)
            optimizer.zero_grad()
            outputs = model(images)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            running_loss += loss.item() * images.size(0)
            _, predicted = outputs.max(1)
            correct += predicted.eq(labels).sum().item()
            total += labels.size(0)
            # Save checkpoint every n minutes if requested
            if save_every_minutes is not None:
                now = time.time()
                if now - last_save_time >= save_every_minutes * 60:
                    save_checkpoint(
                        model,
                        optimizer,
                        epoch,
                        checkpoint_dir,
                        tag=f"batch{batch_idx}",
                        model_type=model_type,
                    )
                    last_save_time = now
        train_loss = running_loss / total
        train_acc = correct / total
        # Validation
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        with torch.no_grad():
            for images, labels in test_loader:
                images, labels = images.to(device), labels.to(device)
                outputs = model(images)
                loss = criterion(outputs, labels)
                val_loss += loss.item() * images.size(0)
                _, predicted = outputs.max(1)
                val_correct += predicted.eq(labels).sum().item()
                val_total += labels.size(0)
        val_loss /= val_total
        val_acc = val_correct / val_total
        log_epoch_summary(
            epoch,
            train_loss,
            train_acc,
            val_loss,
            val_acc,
        )
        save_checkpoint(model, optimizer, epoch, checkpoint_dir, model_type=model_type)


def evaluate_model(model, data_loader, device=None):
    """
    Evaluate the model on a dataset and return accuracy.
    Args:
        model: The neural network to evaluate.
        data_loader: DataLoader for the dataset to evaluate on.
        device: torch.device to use (CPU by default).
    Returns:
        accuracy (float): Fraction of correct predictions.
    """
    if device is None:
        device = torch.device("cpu")
    model.to(device)
    model.eval()
    correct = 0
    total = 0
    with torch.no_grad():
        for images, labels in data_loader:
            images, labels = images.to(device), labels.to(device)
            outputs = model(images)
            _, predicted = outputs.max(1)
            correct += predicted.eq(labels).sum().item()
            total += labels.size(0)
    accuracy = correct / total if total > 0 else 0.0
    return accuracy


def log_epoch_summary(epoch, train_loss, train_acc, val_loss, val_acc):
    """Log epoch summary in a Pylint-compliant way."""
    logger.info(
        "Epoch %d: Train Loss=%f, Train Acc=%f, Val Loss=%f, Val Acc=%f",
        epoch,
        train_loss,
        train_acc,
        val_loss,
        val_acc,
    )


# pylint: disable=too-many-locals,too-many-statements
def train_main(
    args=None,
) -> None:
    # Disable warnings for CLI main function - complexity is acceptable for argument parsing
    # and orchestration
    """
    Main CLI entry point for training MNIST models with checkpointing and time limits.
    Handles argument parsing, data loading, model selection, and time-limited training loop.
    Too many locals/statements warning is suppressed for CLI clarity and maintainability.
    """
    if args is None:
        parser = argparse.ArgumentParser(
            description="Train MNIST model with checkpointing and time limit."
        )
        parser.add_argument(
            "--minutes",
            type=float,
            default=1,
            help="Number of minutes to train (default: 1)",
        )
        parser.add_argument(
            "--test-mode",
            action="store_true",
            help="Use minimal data and fast settings for testing.",
        )
        parser.add_argument(
            "--batch-size",
            type=int,
            default=64,
            help="Batch size for training and validation.",
        )
        parser.add_argument("--lr", type=float, default=0.01, help="Learning rate.")
        parser = add_common_arguments(parser)
        args = parser.parse_args()
    if args.test_mode:
        transform = transforms.Compose(
            [
                transforms.ToTensor(),
            ]
        )
        train_dataset = PatchedMNIST(
            root=args.data_dir, train=True, download=True, transform=transform
        )
        test_dataset = PatchedMNIST(
            root=args.data_dir, train=False, download=True, transform=transform
        )
        train_dataset = Subset(train_dataset, range(8))
        test_dataset = Subset(test_dataset, range(8))
        train_loader = DataLoader(
            train_dataset, batch_size=args.batch_size, shuffle=True
        )
        test_loader = DataLoader(
            test_dataset, batch_size=args.batch_size, shuffle=False
        )
        max_minutes = 0.01
    else:
        train_loader, test_loader = get_mnist_dataloaders(
            data_dir=args.data_dir, batch_size=args.batch_size
        )
        max_minutes = args.minutes
    model, model_type = get_model_from_args(args)
    logger.info(
        "Using %s model.",
        "MinimalMNISTCNN (compact CNN)" if model_type == "cnn" else "SimpleMNISTModel (MLP)",
    )
    start_time = time.time()
    max_seconds = max_minutes * 60
    epochs = 100
    checkpoint_dir = args.checkpoint_dir
    save_every_minutes = 0.5

    def time_limited_train():
        for epoch in range(1, epochs + 1):
            if time.time() - start_time > max_seconds:
                logger.warning("Training stopped after %f minute(s).", max_minutes)
                break
            train_model(
                model,
                train_loader,
                test_loader,
                epochs=epoch,
                lr=args.lr,
                device=None,
                checkpoint_dir=checkpoint_dir,
                save_every_minutes=save_every_minutes,
                model_type=model_type,
            )

    time_limited_train()
    logger.info("Training session complete.")


if __name__ == "__main__":
    try:
        train_main()
    except KeyboardInterrupt:
        logger.warning("Training interrupted by user.")
    except Exception as e:  # pylint: disable=broad-exception-caught
        # Catching all exceptions to ensure uncaught errors are logged in CLI usage.
        logger.error("Uncaught exception: %s", e, exc_info=True)
        print(f"[ERROR] {e}")
