"""Shared helpers for checkpoint loading and model config extraction for MNIST scripts."""
import os
import glob
import torch

def get_model_config(model, model_type):
    """Return model configuration dictionary for checkpointing and validation."""
    if model_type == "cnn":
        return {
            "conv1_out": (
                getattr(model, "conv1", None)[0].out_channels
                if hasattr(model, "conv1")
                else None
            ),
            "conv2_out": (
                getattr(model, "conv2", None)[0].out_channels
                if hasattr(model, "conv2")
                else None
            ),
            "conv3_out": (
                getattr(model, "conv3", None)[0].out_channels
                if hasattr(model, "conv3")
                else None
            ),
            "fc_out": model.fc1.out_features if hasattr(model, "fc1") else None,
        }
    return {"type": "mlp"}

def load_latest_checkpoint(
    model, optimizer, checkpoint_dir="./checkpoints", model_type="mlp"
):
    """
    Load the latest checkpoint if available.
    Args:
        model: The model to load state into.
        optimizer: The optimizer to load state into.
        checkpoint_dir (str): Directory where checkpoints are stored.
        model_type (str): Model type string for filename (e.g., 'mlp', 'cnn').
    Returns:
        start_epoch (int): The epoch to resume from (next epoch).
    """
    pattern = os.path.join(checkpoint_dir, f"mnist_{model_type}_epoch_*.pt")
    checkpoint_files = glob.glob(pattern)
    if not checkpoint_files:
        print("No checkpoint found. Starting from scratch.")
        return 1  # Start from epoch 1 if no checkpoint
    latest_ckpt = max(checkpoint_files, key=os.path.getctime)
    checkpoint = torch.load(latest_ckpt)
    # Check model config
    checkpoint_config = checkpoint.get("model_config", None)
    current_config = get_model_config(model, model_type)
    if checkpoint_config != current_config:
        print(
            "Model architecture mismatch!\nCheckpoint config:", checkpoint_config,
            "\nCurrent config:", current_config,
            "\nAborting load."
        )
        raise ValueError("Model architecture does not match checkpoint.")
    model.load_state_dict(checkpoint["model_state_dict"])
    optimizer.load_state_dict(checkpoint["optimizer_state_dict"])
    print("Loaded checkpoint:", latest_ckpt)
    return checkpoint["epoch"] + 1  # Resume from next epoch 