"""Shared MNIST model definitions for CLI and training/inference/validation scripts."""

import torch
from torch import nn
import torch.nn.functional as F

# MinimalMNISTCNN and conv_block centralized from previous mnist_cnn.py

def conv_block(
    in_channels: int,
    out_channels: int,
    kernel_size: int,
    pool: bool = False,
    batch_norm: bool = False,
) -> nn.Sequential:
    """Create a convolutional block with optional pooling and batch normalization."""
    layers = [nn.Conv2d(in_channels, out_channels, kernel_size, padding=1)]
    if batch_norm:
        layers.append(nn.BatchNorm2d(out_channels))
    layers.append(nn.ReLU(inplace=True))
    if pool:
        layers.append(nn.MaxPool2d(2))
    return nn.Sequential(*layers)

class MinimalMNISTCNN(nn.Module):
    """
    Minimal CNN for MNIST digit classification.
    Architecture: Conv-BN-ReLU-Pool x2 -> Conv-BN-ReLU -> FC
    """
    def __init__(
        self,
        conv1_out: int = 16,
        conv2_out: int = 32,
        conv3_out: int = 32,
        fc_out: int = 10,
    ):
        """Initialize MinimalMNISTCNN with configurable layer sizes."""
        super().__init__()
        self.conv1 = conv_block(1, conv1_out, 3, pool=True, batch_norm=True)
        self.conv2 = conv_block(conv1_out, conv2_out, 3, pool=True, batch_norm=True)
        self.conv3 = conv_block(conv2_out, conv3_out, 3, pool=False, batch_norm=True)
        self.fc1 = nn.Linear(conv3_out * 7 * 7, fc_out)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass for the CNN."""
        x = self.conv1(x)
        x = self.conv2(x)
        x = self.conv3(x)
        x = x.view(x.size(0), -1)
        x = self.fc1(x)
        return x

# SimpleMNISTModel from mnist_train.py
class SimpleMNISTModel(nn.Module):
    """
    A simple feedforward neural network for MNIST digit classification.
    Architecture: Flatten -> Linear(784, 128) -> ReLU -> Linear(128, 10)
    """
    def __init__(self):
        """Initialize SimpleMNISTModel with two fully connected layers."""
        super().__init__()
        self.fc1 = nn.Linear(28 * 28, 128)
        self.fc2 = nn.Linear(128, 10)

    def forward(self, x):
        """Forward pass for the MLP."""
        x = x.view(x.size(0), -1)
        x = F.relu(self.fc1(x))
        x = self.fc2(x)
        return x
