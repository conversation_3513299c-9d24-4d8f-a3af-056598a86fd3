# pylint: disable=R0801
"""Utility script to upgrade old MNIST checkpoints with model config metadata."""

import glob
import os
import torch
from mnist_checkpoint_helpers import get_model_config


def upgrade_checkpoints(
    ckpt_dir: str,
    ckpt_model,
    ckpt_model_type_str: str,
):
    """
    Upgrade old checkpoints to include model configuration metadata.
    Args:
        ckpt_dir: Directory containing checkpoints
        model: The model instance
        model_type_str: Type of model ('cnn' or 'mlp')
    """
    pattern = os.path.join(ckpt_dir, f"mnist_{ckpt_model_type_str}_epoch_*.pt")
    files = glob.glob(pattern)
    if not files:
        print(f"No checkpoints found in {ckpt_dir} for model type {ckpt_model_type_str}.")
        return
    config = get_model_config(ckpt_model, ckpt_model_type_str)
    # Main processing loop
    for checkpoint_file in files:
        ckpt = torch.load(checkpoint_file)
        if "model_config" in ckpt:
            print(f"[SKIP] {checkpoint_file} already has model_config.")
            continue
        ckpt["model_config"] = config
        torch.save(ckpt, checkpoint_file)
        msg = (
            f"[UPGRADED] {checkpoint_file} "
            f"now has model_config:"
        )
        print(msg)
        config_str = str(config)
        print(config_str)


if __name__ == "__main__":
    from mnist_cli_helpers import parse_and_load_model_with_checkpoint
    args, main_model, main_model_type_str, _, _ = \
        parse_and_load_model_with_checkpoint(
            description="Upgrade old checkpoints to include model_config.",
            default_batch_size=64,
            default_test_mode=False
        )
    upgrade_checkpoints(
        args.checkpoint_dir,
        main_model,
        main_model_type_str,
    )
